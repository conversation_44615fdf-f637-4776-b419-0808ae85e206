import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

class SinusoidalPositionEmbeddings(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings

class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, time_emb_dim, dropout=0.1):
        super().__init__()
        self.time_mlp = nn.Linear(time_emb_dim, out_channels)
        self.conv1 = nn.Conv1d(in_channels, out_channels, 3, padding=1)
        self.conv2 = nn.Conv1d(out_channels, out_channels, 3, padding=1)
        self.norm1 = nn.GroupNorm(8, out_channels)
        self.norm2 = nn.GroupNorm(8, out_channels)
        self.dropout = nn.Dropout(dropout)
        
        if in_channels != out_channels:
            self.residual_conv = nn.Conv1d(in_channels, out_channels, 1)
        else:
            self.residual_conv = nn.Identity()

    def forward(self, x, time_emb):
        h = self.conv1(x)
        h = self.norm1(h)
        h = F.relu(h)
        
        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)
        h = h + time_emb.unsqueeze(-1)
        
        h = self.conv2(h)
        h = self.norm2(h)
        h = self.dropout(h)
        h = F.relu(h)
        
        return h + self.residual_conv(x)

class AttentionBlock(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.norm = nn.GroupNorm(8, channels)
        self.q = nn.Conv1d(channels, channels, 1)
        self.k = nn.Conv1d(channels, channels, 1)
        self.v = nn.Conv1d(channels, channels, 1)
        self.proj_out = nn.Conv1d(channels, channels, 1)

    def forward(self, x):
        h = self.norm(x)
        q = self.q(h)
        k = self.k(h)
        v = self.v(h)
        
        # 计算注意力
        b, c, t = q.shape
        q = q.reshape(b, c, t)
        k = k.reshape(b, c, t)
        v = v.reshape(b, c, t)
        
        attn = torch.einsum('bct,bcs->bts', q, k) * (int(c) ** (-0.5))
        attn = F.softmax(attn, dim=-1)
        
        h = torch.einsum('bts,bcs->bct', attn, v)
        h = self.proj_out(h)
        
        return x + h

class UNet1D(nn.Module):
    def __init__(self, in_channels=512, out_channels=512, time_emb_dim=128, 
                 base_channels=64, channel_mults=[1, 2, 4, 8]):
        super().__init__()
        self.time_emb_dim = time_emb_dim
        
        # 时间嵌入
        self.time_mlp = nn.Sequential(
            SinusoidalPositionEmbeddings(time_emb_dim),
            nn.Linear(time_emb_dim, time_emb_dim * 4),
            nn.GELU(),
            nn.Linear(time_emb_dim * 4, time_emb_dim),
        )
        
        # 输入投影
        self.input_proj = nn.Conv1d(in_channels, base_channels, 3, padding=1)
        
        # 下采样路径
        self.downs = nn.ModuleList()
        channels = [base_channels]
        now_channels = base_channels
        
        for mult in channel_mults:
            out_channels_block = base_channels * mult
            self.downs.append(nn.ModuleList([
                ResidualBlock(now_channels, out_channels_block, time_emb_dim),
                ResidualBlock(out_channels_block, out_channels_block, time_emb_dim),
                AttentionBlock(out_channels_block),
                nn.Conv1d(out_channels_block, out_channels_block, 3, stride=2, padding=1)
            ]))
            channels.append(out_channels_block)
            now_channels = out_channels_block
        
        # 中间层
        self.mid_block1 = ResidualBlock(now_channels, now_channels, time_emb_dim)
        self.mid_attn = AttentionBlock(now_channels)
        self.mid_block2 = ResidualBlock(now_channels, now_channels, time_emb_dim)
        
        # 上采样路径
        self.ups = nn.ModuleList()
        for mult in reversed(channel_mults):
            out_channels_block = base_channels * mult
            self.ups.append(nn.ModuleList([
                ResidualBlock(now_channels + channels.pop(), out_channels_block, time_emb_dim),
                ResidualBlock(out_channels_block, out_channels_block, time_emb_dim),
                AttentionBlock(out_channels_block),
                nn.ConvTranspose1d(out_channels_block, out_channels_block, 4, stride=2, padding=1)
            ]))
            now_channels = out_channels_block
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.GroupNorm(8, base_channels),
            nn.ReLU(),
            nn.Conv1d(base_channels, in_channels, 3, padding=1)
        )

    def forward(self, x, timestep):
        # x: [B, C, T]
        time_emb = self.time_mlp(timestep)
        
        x = self.input_proj(x)
        
        # 下采样
        skip_connections = []
        for resnet1, resnet2, attn, downsample in self.downs:
            x = resnet1(x, time_emb)
            x = resnet2(x, time_emb)
            x = attn(x)
            skip_connections.append(x)
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x, time_emb)
        x = self.mid_attn(x)
        x = self.mid_block2(x, time_emb)
        
        # 上采样
        for resnet1, resnet2, attn, upsample in self.ups:
            x = torch.cat([x, skip_connections.pop()], dim=1)
            x = resnet1(x, time_emb)
            x = resnet2(x, time_emb)
            x = attn(x)
            x = upsample(x)
        
        return self.output_proj(x)

class DiffusionFeatureEnhancer(nn.Module):
    def __init__(self, feature_dim=512, timesteps=1000, beta_start=0.0001, beta_end=0.02):
        super().__init__()
        self.feature_dim = feature_dim
        self.timesteps = timesteps
        
        # 噪声调度
        self.register_buffer('betas', torch.linspace(beta_start, beta_end, timesteps))
        self.register_buffer('alphas', 1.0 - self.betas)
        self.register_buffer('alphas_cumprod', torch.cumprod(self.alphas, dim=0))
        self.register_buffer('sqrt_alphas_cumprod', torch.sqrt(self.alphas_cumprod))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', 
                           torch.sqrt(1.0 - self.alphas_cumprod))
        
        # 噪声预测网络
        self.noise_predictor = UNet1D(
            in_channels=feature_dim,
            out_channels=feature_dim,
            time_emb_dim=128
        )
        
    def forward_diffusion(self, x0, t, noise=None):
        """前向扩散过程：添加噪声"""
        if noise is None:
            noise = torch.randn_like(x0)
        
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].reshape(-1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].reshape(-1, 1, 1)
        
        return sqrt_alphas_cumprod_t * x0 + sqrt_one_minus_alphas_cumprod_t * noise, noise
    
    def reverse_diffusion_step(self, xt, t):
        """反向扩散步骤：去噪"""
        predicted_noise = self.noise_predictor(xt, t)
        
        alpha_t = self.alphas[t].reshape(-1, 1, 1)
        alpha_cumprod_t = self.alphas_cumprod[t].reshape(-1, 1, 1)
        beta_t = self.betas[t].reshape(-1, 1, 1)
        
        # 计算去噪后的特征
        x0_pred = (xt - torch.sqrt(1 - alpha_cumprod_t) * predicted_noise) / torch.sqrt(alpha_cumprod_t)
        
        if t[0] > 0:
            noise = torch.randn_like(xt)
            alpha_cumprod_prev = self.alphas_cumprod[t-1].reshape(-1, 1, 1)
            
            # DDPM采样公式
            mean = (torch.sqrt(alpha_cumprod_prev) * beta_t * x0_pred + 
                   torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev) * xt) / (1 - alpha_cumprod_t)
            variance = beta_t * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t)
            
            return mean + torch.sqrt(variance) * noise
        else:
            return x0_pred
    
    def enhance_features(self, features, num_steps=50):
        """使用Diffusion模型增强特征"""
        # 添加少量噪声
        t = torch.randint(0, num_steps, (features.shape[0],), device=features.device)
        noisy_features, _ = self.forward_diffusion(features, t)
        
        # 逐步去噪增强特征
        for i in reversed(range(num_steps)):
            t_batch = torch.full((features.shape[0],), i, device=features.device, dtype=torch.long)
            noisy_features = self.reverse_diffusion_step(noisy_features, t_batch)
        
        return noisy_features
    
    def compute_loss(self, features):
        """计算扩散损失"""
        batch_size = features.shape[0]
        t = torch.randint(0, self.timesteps, (batch_size,), device=features.device)
        noise = torch.randn_like(features)
        
        noisy_features, _ = self.forward_diffusion(features, t, noise)
        predicted_noise = self.noise_predictor(noisy_features, t)
        
        return F.mse_loss(predicted_noise, noise)
