feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014-T
num_epoch: 50
work_dir: ./work_dir/diffusion_enhanced_slr/
batch_size: 2
random_seed: 0
test_batch_size: 2
num_worker: 10
device: 0
log_interval: 10000
eval_interval: 1
save_interval: 5
evaluate_tool: python
print_log: True
load_weights: null
load_checkpoints: null
random_fix: True

# 损失权重配置 - 添加Diffusion损失
loss_weights:
  SeqCTC: 1.0
  ConvCTC: 1.0
  Dist: 25.0
  Diffusion: 0.1  # Diffusion特征增强损失权重

optimizer_args:
  optimizer: Adam
  base_lr: 0.0001
  step: [25, 40]
  learning_ratio: 1
  weight_decay: 0.0001
  start_epoch: 0
  nesterov: False

feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0
  input_size: 224

model: slr_network.SLRModel
decode_mode: beam
model_args:
  num_classes: 1296
  c2d_type: resnet18
  conv_type: 2
  use_bn: 1
  share_classifier: True
  weight_norm: True
  # Diffusion相关参数
  use_diffusion: True
  diffusion_steps: 50

# 动态时间注意力机制参数
attention_params:
  max_window_size: 11
  kernel_sizes: [5, 9, 13]
  reduction_ratio: 16

# Diffusion模型特定参数
diffusion_params:
  timesteps: 1000
  beta_start: 0.0001
  beta_end: 0.02
  hidden_dim: 512
  num_layers: 6
  num_heads: 8
  use_temporal_diffusion: True
  feature_enhancement: True
  sequence_generation: True
