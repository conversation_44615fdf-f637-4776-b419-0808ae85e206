import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from modules.diffusion_enhancement import SinusoidalPositionEmbeddings

class TemporalTransformerBlock(nn.Module):
    def __init__(self, hidden_dim, num_heads=8, dropout=0.1):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        
        self.self_attn = nn.MultiheadAttention(
            hidden_dim, num_heads, dropout=dropout, batch_first=True
        )
        
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x, time_emb=None):
        # x: [B, T, C]
        residual = x
        x = self.norm1(x)
        
        # 如果有时间嵌入，添加到查询中
        if time_emb is not None:
            x = x + time_emb.unsqueeze(1)
        
        attn_out, _ = self.self_attn(x, x, x)
        x = residual + attn_out
        
        # FFN
        residual = x
        x = self.norm2(x)
        x = residual + self.ffn(x)
        
        return x

class TemporalUNet(nn.Module):
    def __init__(self, vocab_size, hidden_dim=512, num_layers=6, num_heads=8, max_length=200):
        super().__init__()
        self.vocab_size = vocab_size
        self.hidden_dim = hidden_dim
        self.max_length = max_length
        
        # 词嵌入和位置嵌入
        self.token_embedding = nn.Embedding(vocab_size, hidden_dim)
        self.pos_embedding = nn.Embedding(max_length, hidden_dim)
        
        # 时间嵌入
        self.time_mlp = nn.Sequential(
            SinusoidalPositionEmbeddings(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.GELU(),
            nn.Linear(hidden_dim * 4, hidden_dim),
        )
        
        # 条件嵌入（视觉特征）
        self.condition_proj = nn.Linear(512, hidden_dim)  # 假设视觉特征维度为512
        
        # Transformer层
        self.layers = nn.ModuleList([
            TemporalTransformerBlock(hidden_dim, num_heads)
            for _ in range(num_layers)
        ])
        
        # 输出投影
        self.output_proj = nn.Linear(hidden_dim, vocab_size)
        
        # 交叉注意力层（用于条件生成）
        self.cross_attn_layers = nn.ModuleList([
            nn.MultiheadAttention(hidden_dim, num_heads, batch_first=True)
            for _ in range(num_layers)
        ])
        
    def forward(self, x, timestep, condition=None):
        # x: [B, T] - 噪声序列（token indices）
        # timestep: [B] - 时间步
        # condition: [B, T_vis, C_vis] - 视觉特征条件
        
        B, T = x.shape
        
        # Token嵌入
        if x.dtype == torch.long:
            x_emb = self.token_embedding(x)
        else:
            # 如果是连续值（噪声），直接使用
            x_emb = x
        
        # 位置嵌入
        pos_ids = torch.arange(T, device=x.device).unsqueeze(0).expand(B, -1)
        pos_emb = self.pos_embedding(pos_ids)
        
        # 时间嵌入
        time_emb = self.time_mlp(timestep)
        
        # 组合嵌入
        h = x_emb + pos_emb
        
        # 处理条件
        if condition is not None:
            condition = self.condition_proj(condition)
        
        # 通过Transformer层
        for i, (layer, cross_attn) in enumerate(zip(self.layers, self.cross_attn_layers)):
            h = layer(h, time_emb)
            
            # 交叉注意力（条件生成）
            if condition is not None:
                residual = h
                h_norm = F.layer_norm(h, h.shape[-1:])
                cross_out, _ = cross_attn(h_norm, condition, condition)
                h = residual + cross_out
        
        # 输出投影
        output = self.output_proj(h)
        
        return output

class TemporalDiffusionModel(nn.Module):
    def __init__(self, vocab_size, hidden_dim=512, timesteps=1000, 
                 beta_start=0.0001, beta_end=0.02):
        super().__init__()
        self.vocab_size = vocab_size
        self.timesteps = timesteps
        
        # 噪声调度
        self.register_buffer('betas', torch.linspace(beta_start, beta_end, timesteps))
        self.register_buffer('alphas', 1.0 - self.betas)
        self.register_buffer('alphas_cumprod', torch.cumprod(self.alphas, dim=0))
        self.register_buffer('sqrt_alphas_cumprod', torch.sqrt(self.alphas_cumprod))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', 
                           torch.sqrt(1.0 - self.alphas_cumprod))
        
        # 时序UNet
        self.temporal_unet = TemporalUNet(vocab_size, hidden_dim)
        
    def q_sample(self, x_start, t, noise=None):
        """前向扩散过程"""
        if noise is None:
            noise = torch.randn_like(x_start.float())
        
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].reshape(-1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].reshape(-1, 1)
        
        return (sqrt_alphas_cumprod_t * x_start.float() + 
                sqrt_one_minus_alphas_cumprod_t * noise)
    
    def p_sample_step(self, x_t, t, condition=None):
        """反向扩散单步"""
        # 预测噪声
        predicted_noise = self.temporal_unet(x_t, t, condition)
        
        # 计算去噪参数
        alpha_t = self.alphas[t].reshape(-1, 1)
        alpha_cumprod_t = self.alphas_cumprod[t].reshape(-1, 1)
        beta_t = self.betas[t].reshape(-1, 1)
        
        # 预测x_0
        x_0_pred = (x_t - torch.sqrt(1 - alpha_cumprod_t) * predicted_noise) / torch.sqrt(alpha_cumprod_t)
        
        if t[0] > 0:
            noise = torch.randn_like(x_t)
            alpha_cumprod_prev = self.alphas_cumprod[t-1].reshape(-1, 1)
            
            # DDPM采样公式
            mean = (torch.sqrt(alpha_cumprod_prev) * beta_t * x_0_pred + 
                   torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev) * x_t) / (1 - alpha_cumprod_t)
            variance = beta_t * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t)
            
            return mean + torch.sqrt(variance) * noise
        else:
            return x_0_pred
    
    def p_sample(self, shape, condition=None, num_steps=None):
        """完整的反向扩散采样"""
        if num_steps is None:
            num_steps = self.timesteps
        
        device = next(self.parameters()).device
        
        # 从纯噪声开始
        x = torch.randn(shape, device=device)
        
        # 逐步去噪
        for i in reversed(range(num_steps)):
            t = torch.full((shape[0],), i, device=device, dtype=torch.long)
            x = self.p_sample_step(x, t, condition)
        
        return x
    
    def compute_loss(self, x_start, condition=None):
        """计算训练损失"""
        batch_size = x_start.shape[0]
        device = x_start.device
        
        # 随机选择时间步
        t = torch.randint(0, self.timesteps, (batch_size,), device=device)
        
        # 添加噪声
        noise = torch.randn_like(x_start.float())
        x_noisy = self.q_sample(x_start, t, noise)
        
        # 预测噪声
        predicted_noise = self.temporal_unet(x_noisy, t, condition)
        
        # 计算损失
        loss = F.mse_loss(predicted_noise, noise)
        
        return loss
    
    def generate_sequence(self, condition, max_length=50, num_steps=50):
        """生成手语序列"""
        batch_size = condition.shape[0]
        shape = (batch_size, max_length)
        
        # 采样连续值
        continuous_seq = self.p_sample(shape, condition, num_steps)
        
        # 转换为离散token（通过argmax或Gumbel softmax）
        # 这里使用简单的线性映射然后argmax
        logits = self.temporal_unet.output_proj(continuous_seq.unsqueeze(-1).expand(-1, -1, self.temporal_unet.hidden_dim))
        discrete_seq = torch.argmax(logits, dim=-1)
        
        return discrete_seq

class DiffusionEnhancedSLR(nn.Module):
    """集成Diffusion的增强版手语识别模型"""
    def __init__(self, base_model, vocab_size, use_temporal_diffusion=True):
        super().__init__()
        self.base_model = base_model
        self.use_temporal_diffusion = use_temporal_diffusion
        
        if use_temporal_diffusion:
            self.temporal_diffusion = TemporalDiffusionModel(vocab_size)
        
    def forward(self, x, len_x, label=None, label_lgt=None):
        # 基础模型前向传播
        base_output = self.base_model(x, len_x, label, label_lgt)
        
        if self.training and self.use_temporal_diffusion and label is not None:
            # 训练时计算Diffusion损失
            visual_features = base_output.get('visual_features')
            if visual_features is not None:
                diffusion_loss = self.temporal_diffusion.compute_loss(
                    label.float(), visual_features
                )
                base_output['diffusion_loss'] = diffusion_loss
        
        elif not self.training and self.use_temporal_diffusion:
            # 推理时使用Diffusion生成增强预测
            visual_features = base_output.get('visual_features')
            if visual_features is not None:
                enhanced_pred = self.temporal_diffusion.generate_sequence(
                    visual_features, max_length=base_output['feat_len'].max()
                )
                base_output['diffusion_pred'] = enhanced_pred
        
        return base_output
